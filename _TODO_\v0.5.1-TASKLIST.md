# v0.5.1 Tasklist

- [ ] Audit `requirements.txt` for unused or dev-only packages (e.g., pip-audit, pip-api, pip-requirements-parser, cyclonedx-python-lib, boolean.py)
- [ ] Remove or move unnecessary packages to a dev requirements file
- [ ] Run a security audit after dependency changes (e.g., with trivy or pip-audit)
- [ ] Replace `paramiko.AutoAddPolicy()` with strict host key verification in SSH manager
- [ ] Add configuration or documentation for managing known hosts
- [ ] Identify untested modules, especially SSH and API key management
- [ ] Add unit and integration tests, focusing on security and error handling
- [ ] Ensure all tests are in the `tests/` directory and follow pytest conventions
- [ ] Review `docs/` and `docs/notion/` for duplicate or outdated content
- [ ] Consolidate and update documentation for clarity and accuracy
- [ ] Ensure the main README is up-to-date and references all key features
- [ ] Schedule periodic dependency and code security scans
- [ ] Document the security audit process for future contributors
- [ ] Ensure all sensitive data (API keys, secrets) are loaded from environment variables
- [ ] Update documentation to reflect environment variable best practices
- [ ] Continue to standardize logging and error handling across modules
- [ ] Refactor any remaining legacy or inconsistent code patterns 