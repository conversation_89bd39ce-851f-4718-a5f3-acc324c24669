{"nodes": [{"type": "text", "id": "a3b5797d-d03b-4e0c-b4d7-ddb89328c5e0", "x": -349, "y": 174, "width": 106, "height": 108, "color": "6", "text": "## <center>User</center>  \n <center>Player</center> "}, {"type": "nested-canvas", "id": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "x": -38.105103312989854, "y": 178.73960886205782, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "x": 268, "y": 308.57142857142856, "width": 173, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/app.py\"]\n---\n\n \n ## <center>Web Server</center>  \n  <center>Python Flask</center> "}, {"type": "text", "id": "15f7f6e6-87dd-4340-94f0-571706bf0899", "x": 906, "y": 586, "width": 235, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/terminal/terminal_manager.py\"]\n---\n\n \n ## <center>Terminal Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "cd2324f4-4c6e-4759-9301-4cbbea68434c", "x": 891, "y": 270, "width": 197, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\"]\n---\n\n \n ## <center>Chat Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "9e400c9d-6c74-4fd7-a4d0-69b79e148c33", "x": 1306, "y": 586, "width": 182, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/ssh/ssh_manager.py\"]\n---\n\n \n ## <center>SSH Handler</center>  \n  <center>Python Paramiko</center> "}, {"type": "text", "id": "85b26ae0-76a3-48d9-b23a-91b642a62425", "x": 891, "y": 902, "width": 265, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/progression_manager.py\"]\n---\n\n \n ## <center>Progression Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "2719c9e3-c264-4d8d-853d-cc421f268d97", "x": 891, "y": 744, "width": 223, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/session_manager.py\"]\n---\n\n \n ## <center>Session Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "a6cfaa8b-b79a-4fb2-8824-5f9f042d4b97", "x": 891, "y": 428, "width": 212, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/security_logger.py\"]\n---\n\n \n ## <center>Security Logger</center>  \n  <center>Python</center> "}, {"type": "text", "id": "0a0ddd08-2d73-4814-96c6-016a584bf3a1", "x": 12, "y": 12, "width": 226, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\"]\n---\n\n \n ## <center>API Key Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "75489726-902b-4c02-952f-fc77abb61a72", "x": 1306, "y": 920, "width": 242, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/level_info.py\",\"banditgui/data/levels_info.json\",\"banditgui/data/general_info.json\",\"banditgui/data/geek_quotes.json\",\"banditgui/data/commands_data.json\",\"banditgui/data/all_data.json\"]\n---\n\n \n ## <center>Level Data Handler</center>  \n  <center>Python</center> "}, {"type": "text", "id": "961f7686-96e9-4cd3-9511-16c42dfcb6a8", "x": 12, "y": 308.57142857142856, "width": 106, "height": 108, "color": "1", "text": "## <center>User</center>  \n <center>Player</center> "}], "edges": [{"id": "3c9d31b1-5712-400b-857e-94dc2c6f19ad", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "15f7f6e6-87dd-4340-94f0-571706bf0899", "toSide": "left", "toEnd": "arrow", "label": "Manages terminal"}, {"id": "a673d0eb-d911-43fb-9a5b-725f84534001", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "cd2324f4-4c6e-4759-9301-4cbbea68434c", "toSide": "left", "toEnd": "arrow", "label": "Handles chat"}, {"id": "5410e44e-9d67-4dad-91ea-34c5f5110f8e", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "2719c9e3-c264-4d8d-853d-cc421f268d97", "toSide": "left", "toEnd": "arrow", "label": "Manages sessions"}, {"id": "e4b500a8-0b87-42d8-ae1e-950ec8f4c825", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "85b26ae0-76a3-48d9-b23a-91b642a62425", "toSide": "left", "toEnd": "arrow", "label": "Updates progress"}, {"id": "f85337a0-baf5-4034-9286-93036deca692", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "a6cfaa8b-b79a-4fb2-8824-5f9f042d4b97", "toSide": "left", "toEnd": "arrow", "label": "Logs events"}, {"id": "99d9f41e-9660-46e3-92af-06b45388f5a7", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "75489726-902b-4c02-952f-fc77abb61a72", "toSide": "left", "toEnd": "arrow", "label": "Retrieves level info"}, {"id": "7ffc3951-ddaf-4ac5-b756-4fb3c589fd2f", "fromNode": "15f7f6e6-87dd-4340-94f0-571706bf0899", "fromSide": "right", "toNode": "9e400c9d-6c74-4fd7-a4d0-69b79e148c33", "toSide": "left", "toEnd": "arrow", "label": "Initiates SSH"}, {"id": "6c088d5e-f3db-4d00-95f9-793e6484e2f2", "fromNode": "85b26ae0-76a3-48d9-b23a-91b642a62425", "fromSide": "right", "toNode": "75489726-902b-4c02-952f-fc77abb61a72", "toSide": "left", "toEnd": "arrow", "label": "Reads level data"}, {"id": "1a1e04bb-994f-4163-b47b-efa970ce68dc", "fromNode": "961f7686-96e9-4cd3-9511-16c42dfcb6a8", "fromSide": "right", "toNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "toSide": "left", "toEnd": "arrow", "label": "Accesses UI"}]}, "title": "BanditGUI App | Python, Flask"}, {"type": "nested-canvas", "id": "6b2cc32d-83e3-4e6d-bb39-083b322b727d", "x": 477, "y": 138, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "x": 388, "y": 30, "width": 228, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/main.py\"]\n---\n\n \n ## <center>LLM Orchestrator</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "07bc172b-5cfb-4030-9b71-213d81f0ea2e", "x": 766, "y": 12, "width": 172, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/router.py\"]\n---\n\n \n ## <center>LLM Router</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "1d3ce7c6-485c-4b61-ba43-09199af3ea92", "x": 766, "y": 170, "width": 206, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/cost_calculator.py\"]\n---\n\n \n ## <center>Cost Calculator</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "dbfb3799-53b6-492f-8e0e-9aeca22a789e", "x": 41, "y": 170, "width": 197, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\"]\n---\n\n \n ## <center>Chat Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "7e15f5fd-9c8b-42b9-ba34-97fa529ee51a", "x": 12, "y": 12, "width": 226, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\"]\n---\n\n \n ## <center>API Key Manager</center>  \n  <center>Python</center> "}], "edges": [{"id": "36734006-be87-4d63-ba10-bf4b6a8efc89", "fromNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "fromSide": "right", "toNode": "07bc172b-5cfb-4030-9b71-213d81f0ea2e", "toSide": "left", "toEnd": "arrow", "label": "Routes requests"}, {"id": "ee6fe850-0081-48bb-aa80-0fb2bfea0668", "fromNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "fromSide": "right", "toNode": "1d3ce7c6-485c-4b61-ba43-09199af3ea92", "toSide": "left", "toEnd": "arrow", "label": "Tracks cost"}, {"id": "c198946c-bfca-4581-b6d0-ee33b085b29b", "fromNode": "dbfb3799-53b6-492f-8e0e-9aeca22a789e", "fromSide": "right", "toNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "toSide": "left", "toEnd": "arrow", "label": "Sends prompts"}, {"id": "267b5409-c411-48c1-b4a3-e2ad96a7ffb0", "fromNode": "7e15f5fd-9c8b-42b9-ba34-97fa529ee51a", "fromSide": "right", "toNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "toSide": "left", "toEnd": "arrow", "label": "Provides keys"}]}, "title": "LLM Proxy | Python, LiteLLM"}], "edges": [{"id": "1a1e04bb-994f-4163-b47b-efa970ce68dc", "fromNode": "a3b5797d-d03b-4e0c-b4d7-ddb89328c5e0", "fromSide": "right", "toNode": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "toSide": "left", "toEnd": "arrow", "label": "Accesses UI"}, {"id": "c198946c-bfca-4581-b6d0-ee33b085b29b", "fromNode": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "fromSide": "right", "toNode": "6b2cc32d-83e3-4e6d-bb39-083b322b727d", "toSide": "left", "toEnd": "arrow", "label": "Sends prompts"}]}